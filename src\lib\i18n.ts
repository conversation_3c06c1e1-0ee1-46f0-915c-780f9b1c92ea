// 简单的国际化系统
export type Language = 'en' | 'zh';

export interface Translations {
  // 通用
  loading: string;
  error: string;
  cancel: string;
  submit: string;
  send: string;
  edit: string;
  copy: string;
  refresh: string;
  
  // 主界面
  agentChat: string;
  newThread: string;
  scrollToBottom: string;
  typeYourMessage: string;
  hideToolCalls: string;
  uploadPdfOrImage: string;
  
  // 错误信息
  errorOccurred: string;
  errorTryAgain: string;
  
  // 工具提示
  copyContent: string;
  cancelEdit: string;
  
  // 加载状态
  loadingLayout: string;
  running: string;
  
  // Agent Inbox
  respondToAssistant: string;
  response: string;
  yourResponseHere: string;
  studio: string;
  
  // 其他
  or: string;
  somethingWentWrong: string;
  setLangGraphUrl: string;
  successfullyFinished: string;
  humanInterrupt: string;

  // 语言切换
  switchLanguage: string;

  // 侧边栏
  threadHistory: string;

  // 操作按钮
  markAsResolved: string;
  ignore: string;
  noDescriptionProvided: string;

  // 工具调用
  toolResult: string;
}

const translations: Record<Language, Translations> = {
  en: {
    // 通用
    loading: 'Loading',
    error: 'Error',
    cancel: 'Cancel',
    submit: 'Submit',
    send: 'Send',
    edit: 'Edit',
    copy: 'Copy',
    refresh: 'Refresh',
    
    // 主界面
    agentChat: 'Agent Chat',
    newThread: 'New thread',
    scrollToBottom: 'Scroll to bottom',
    typeYourMessage: 'Type your message...',
    hideToolCalls: 'Hide Tool Calls',
    uploadPdfOrImage: 'Upload PDF or Image',
    
    // 错误信息
    errorOccurred: 'An error occurred. Please try again.',
    errorTryAgain: 'Please try again',
    
    // 工具提示
    copyContent: 'Copy content',
    cancelEdit: 'Cancel edit',
    
    // 加载状态
    loadingLayout: 'Loading (layout)...',
    running: 'Running...',
    
    // Agent Inbox
    respondToAssistant: 'Respond to assistant',
    response: 'Response',
    yourResponseHere: 'Your response here...',
    studio: 'Studio',
    
    // 其他
    or: 'Or',
    somethingWentWrong: 'Something went wrong',
    setLangGraphUrl: 'Please set the LangGraph deployment URL in settings.',
    successfullyFinished: 'Successfully finished Graph invocation.',
    humanInterrupt: 'Human Interrupt',

    // 语言切换
    switchLanguage: 'Switch Language',

    // 侧边栏
    threadHistory: 'Thread History',

    // 操作按钮
    markAsResolved: 'Mark as Resolved',
    ignore: 'Ignore',
    noDescriptionProvided: 'No description provided',

    // 工具调用
    toolResult: 'Tool Result',
  },
  zh: {
    // 通用
    loading: '加载中',
    error: '错误',
    cancel: '取消',
    submit: '提交',
    send: '发送',
    edit: '编辑',
    copy: '复制',
    refresh: '刷新',
    
    // 主界面
    agentChat: '智能助手聊天',
    newThread: '新对话',
    scrollToBottom: '滚动到底部',
    typeYourMessage: '输入您的消息...',
    hideToolCalls: '隐藏工具调用',
    uploadPdfOrImage: '上传PDF或图片',
    
    // 错误信息
    errorOccurred: '发生错误，请重试。',
    errorTryAgain: '请重试',
    
    // 工具提示
    copyContent: '复制内容',
    cancelEdit: '取消编辑',
    
    // 加载状态
    loadingLayout: '加载中（布局）...',
    running: '运行中...',
    
    // Agent Inbox
    respondToAssistant: '回复助手',
    response: '回复',
    yourResponseHere: '在此输入您的回复...',
    studio: '工作室',
    
    // 其他
    or: '或',
    somethingWentWrong: '出现了问题',
    setLangGraphUrl: '请在设置中设置LangGraph部署URL。',
    successfullyFinished: '图表调用成功完成。',
    humanInterrupt: '人工干预',

    // 语言切换
    switchLanguage: '切换语言',

    // 侧边栏
    threadHistory: '对话历史',

    // 操作按钮
    markAsResolved: '标记为已解决',
    ignore: '忽略',
    noDescriptionProvided: '未提供描述',

    // 工具调用
    toolResult: '工具结果',
  },
};

// 当前语言设置
let currentLanguage: Language = 'zh'; // 默认设置为中文

export function setLanguage(lang: Language) {
  currentLanguage = lang;
}

export function getCurrentLanguage(): Language {
  return currentLanguage;
}

export function t(key: keyof Translations): string {
  return translations[currentLanguage][key] || translations.en[key] || key;
}

// 导出翻译函数的简写
export { t as translate };
