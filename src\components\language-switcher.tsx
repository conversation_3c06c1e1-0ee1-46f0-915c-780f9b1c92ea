"use client";

import { Button } from "@/components/ui/button";
import { setLanguage, getCurrentLanguage, t, Language } from "@/lib/i18n";
import { useState } from "react";

export function LanguageSwitcher() {
  const [currentLang, setCurrentLang] = useState<Language>(getCurrentLanguage());

  const toggleLanguage = () => {
    const newLang: Language = currentLang === 'zh' ? 'en' : 'zh';
    setLanguage(newLang);
    setCurrentLang(newLang);
    // 强制重新渲染页面
    window.location.reload();
  };

  return (
    <Button
      variant="outline"
      size="sm"
      onClick={toggleLanguage}
      className="text-xs"
    >
      {currentLang === 'zh' ? 'EN' : '中文'}
    </Button>
  );
}
